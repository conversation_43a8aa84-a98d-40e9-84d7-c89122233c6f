#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本 - 测试已知图书链接的爬取
"""

from tup_crawler import TupCrawler
import json

def test_known_books():
    """测试已知的图书链接"""
    crawler = TupCrawler()
    
    # 一些已知的图书链接（从网站上手动收集）
    known_book_links = [
        "/booksCenter/book_10520401.html",  # 高等数学（上册）
        # 可以添加更多已知的图书链接
    ]
    
    print(f"开始测试 {len(known_book_links)} 个已知图书链接...")
    
    success_count = 0
    for i, book_link in enumerate(known_book_links):
        print(f"\n正在处理第 {i+1} 本图书: {book_link}")
        
        book_info = crawler.get_book_details(book_link)
        if book_info:
            crawler.books_data.append(book_info)
            success_count += 1
            
            print(f"  ✓ 成功提取信息:")
            print(f"    标题: {book_info.get('title', '未知')}")
            print(f"    ISBN: {book_info.get('isbn', '未知')}")
            print(f"    定价: {book_info.get('price', '未知')}")
            print(f"    封面图片: {book_info.get('cover_image_file', '未下载')}")
        else:
            print(f"  ✗ 提取信息失败")
    
    # 保存数据
    if crawler.books_data:
        crawler.save_data()
        print(f"\n测试完成！成功处理 {success_count}/{len(known_book_links)} 本图书")
        print("数据已保存到 books_data 目录")
    else:
        print("\n测试失败，没有成功提取任何图书信息")

def search_for_book_links():
    """尝试搜索图书链接"""
    crawler = TupCrawler()
    
    # 尝试从首页或其他页面搜索图书链接
    search_urls = [
        "https://www.tup.com.cn/index.html",
        "https://www.tup.com.cn/booksCenter/books_index.html",
    ]
    
    all_book_links = []
    
    for url in search_urls:
        print(f"正在搜索页面: {url}")
        response = crawler.safe_request(url)
        
        if response:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找所有链接
            links = soup.find_all('a', href=True)
            book_links = []
            
            for link in links:
                href = link.get('href')
                if href and 'book_' in href and '.html' in href:
                    book_links.append(href)
            
            print(f"  找到 {len(book_links)} 个图书链接")
            all_book_links.extend(book_links)
    
    # 去重
    unique_links = list(set(all_book_links))
    print(f"\n总共找到 {len(unique_links)} 个唯一图书链接")
    
    if unique_links:
        print("前10个链接:")
        for i, link in enumerate(unique_links[:10]):
            print(f"  {i+1}. {link}")
    
    return unique_links

def test_image_download():
    """专门测试图片下载功能"""
    crawler = TupCrawler()
    
    # 测试图片URL
    test_image_url = "../upload/bigbookimg/105204-01.jpg"
    test_title = "高等数学（上册）"
    test_isbn = "9787302684442"
    
    print("测试图片下载功能...")
    print(f"图片URL: {test_image_url}")
    print(f"图书标题: {test_title}")
    print(f"ISBN: {test_isbn}")
    
    filename = crawler.download_cover_image(test_image_url, test_isbn, test_title)
    
    if filename:
        print(f"✓ 图片下载成功: {filename}")
        
        # 检查文件是否存在
        import os
        filepath = os.path.join(crawler.images_dir, filename)
        if os.path.exists(filepath):
            file_size = os.path.getsize(filepath)
            print(f"  文件大小: {file_size:,} bytes")
        else:
            print("  警告: 文件不存在")
    else:
        print("✗ 图片下载失败")

if __name__ == "__main__":
    print("=== 简单测试脚本 ===\n")
    
    print("请选择测试模式:")
    print("1. 测试已知图书链接")
    print("2. 搜索图书链接")
    print("3. 测试图片下载")
    print("4. 运行所有测试")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == "1":
        test_known_books()
    elif choice == "2":
        search_for_book_links()
    elif choice == "3":
        test_image_download()
    elif choice == "4":
        print("=== 测试1: 已知图书链接 ===")
        test_known_books()
        print("\n=== 测试2: 搜索图书链接 ===")
        search_for_book_links()
        print("\n=== 测试3: 图片下载 ===")
        test_image_download()
    else:
        print("无效选择，退出测试")
