# 清华大学出版社图书信息爬虫

这是一个专门用于爬取清华大学出版社官网（https://www.tup.com.cn/）图书信息的Python爬虫程序。

## 功能特性

### 🎯 爬取目标
- **网站**: 清华大学出版社官网 (https://www.tup.com.cn/)
- **内容**: 所有图书的详细信息和封面图片

### 📊 提取信息字段
- **图书名称**: 从页面标题提取
- **定价**: 图书价格信息
- **印次**: 图书印刷次数
- **ISBN**: 国际标准书号
- **出版日期**: 图书出版时间
- **出版机构**: 固定为"清华大学出版社"
- **封面图片**: 下载并保存图书封面图片
- **图片文件名**: 本地保存的图片文件名

### 🛡️ 技术特性
- **遵守robots.txt**: 尊重网站爬虫政策
- **请求间隔控制**: 1-3秒随机间隔，避免服务器负担
- **错误处理**: 完善的异常处理和重试机制
- **数据持久化**: 支持JSON和CSV格式导出
- **图片管理**: 自动下载并组织封面图片
- **日志记录**: 详细的运行日志

## 📁 项目结构

```
Textbook_Library_crawler/
├── tup_crawler.py          # 主爬虫程序
├── test_crawler.py         # 测试脚本
├── requirements.txt        # 依赖包列表
├── README.md              # 项目说明文档
├── venv/                  # Python虚拟环境
└── books_data/            # 输出目录
    ├── books_data.json    # JSON格式数据
    ├── books_data.csv     # CSV格式数据
    ├── crawler.log        # 运行日志
    └── images/            # 封面图片目录
        ├── 9787302684442_高等数学（上册）.jpg
        └── ...
```

## 🚀 安装和使用

### 环境要求
- Python 3.7+
- 已配置的虚拟环境

### 安装依赖
```bash
# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖包
pip install -r requirements.txt
```

### 运行爬虫

#### 1. 完整爬取（推荐）
```bash
python tup_crawler.py
```

#### 2. 测试模式
```bash
python test_crawler.py
```

测试模式提供三种选项：
- **选项1**: 测试单本图书信息提取
- **选项2**: 测试分类页面链接提取  
- **选项3**: 测试有限数量图书爬取（3本）

## 📋 输出说明

### 数据文件
1. **books_data.json**: 完整的JSON格式数据
2. **books_data.csv**: 便于Excel打开的CSV格式数据
3. **crawler.log**: 详细的运行日志

### 图片文件
- 保存在 `books_data/images/` 目录
- 文件命名格式: `{ISBN}_{图书名称}.jpg`
- 自动去重，避免重复下载

### 数据字段说明
| 字段名 | 说明 | 示例 |
|--------|------|------|
| title | 图书名称 | "高等数学（上册）" |
| price | 定价 | "55元" |
| edition | 印次 | "1-1" |
| isbn | ISBN号 | "9787302684442" |
| publish_date | 出版日期 | "2025.05.01" |
| publisher | 出版机构 | "清华大学出版社" |
| cover_image | 封面图片URL | "../upload/bigbookimg/105204-01.jpg" |
| cover_image_file | 本地图片文件名 | "9787302684442_高等数学（上册）.jpg" |
| url | 图书详情页URL | "https://www.tup.com.cn/..." |
| crawl_time | 爬取时间 | "2024-12-19T10:30:00" |

## ⚙️ 配置选项

### 请求间隔设置
在 `tup_crawler.py` 中可以调整：
```python
self.min_delay = 1  # 最小间隔（秒）
self.max_delay = 3  # 最大间隔（秒）
```

### 分类和年份范围
默认爬取所有分类的2020-2025年图书，可在代码中调整：
```python
categories = [2000, 2010, 2020, ...]  # 分类ID
years = [2025, 2024, 2023, 2022, 2021, 2020]  # 年份范围
```

## 🔧 故障排除

### 常见问题

1. **网络连接问题**
   - 检查网络连接
   - 确认目标网站可访问

2. **依赖包问题**
   - 确保虚拟环境已激活
   - 重新安装依赖: `pip install -r requirements.txt`

3. **权限问题**
   - 确保有写入当前目录的权限
   - 检查防火墙设置

4. **图片下载失败**
   - 检查images目录权限
   - 网络不稳定时会自动重试

### 日志查看
运行日志保存在 `books_data/crawler.log`，包含详细的执行信息和错误记录。

## 📝 注意事项

1. **遵守网站政策**: 本爬虫遵循网站的robots.txt规则
2. **合理使用**: 请勿过度频繁访问，避免对服务器造成负担
3. **数据用途**: 爬取的数据仅供学习和研究使用
4. **版权声明**: 图书信息和封面图片版权归清华大学出版社所有

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和网站使用条款。
