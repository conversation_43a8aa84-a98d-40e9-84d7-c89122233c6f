#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度优化版清华大学出版社图书信息爬虫
- 多线程并发处理
- 智能重试机制
- 详细日志记录
- 数据完整性验证
- 性能监控
"""

import requests
from bs4 import BeautifulSoup
import json
import csv
import os
import time
import random
import re
from urllib.parse import urljoin, urlparse
from tqdm import tqdm
import logging
from datetime import datetime
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue
import hashlib
from dataclasses import dataclass, asdict
from typing import List, Optional, Dict, Any
import signal
import sys

@dataclass
class BookInfo:
    """图书信息数据类"""
    url: str = ""
    title: str = ""
    price: str = ""
    edition: str = ""
    isbn: str = ""
    publish_date: str = ""
    publisher: str = "清华大学出版社"
    cover_image: str = ""
    cover_image_file: str = ""
    author: str = ""
    pages: str = ""
    category: str = ""
    description: str = ""
    crawl_time: str = ""
    success: bool = False
    error_msg: str = ""

class AdvancedTupCrawler:
    def __init__(self, max_workers=5, enable_threading=True):
        """初始化高级爬虫"""
        self.base_url = "https://www.tup.com.cn"
        self.max_workers = max_workers
        self.enable_threading = enable_threading
        
        # 创建会话池
        self.session_pool = queue.Queue()
        for _ in range(max_workers):
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Cache-Control': 'max-age=0',
            })
            self.session_pool.put(session)
        
        # 创建输出目录
        self.output_dir = "books_data_advanced"
        self.images_dir = os.path.join(self.output_dir, "images")
        self.logs_dir = os.path.join(self.output_dir, "logs")
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.images_dir, exist_ok=True)
        os.makedirs(self.logs_dir, exist_ok=True)
        
        # 设置详细日志
        self.setup_advanced_logging()
        
        # 性能统计
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'books_found': 0,
            'images_downloaded': 0,
            'start_time': None,
            'end_time': None,
            'processed_urls': set(),
            'failed_urls': set(),
        }
        
        # 存储数据
        self.books_data = []
        self.data_lock = threading.Lock()

        # ISBN去重机制
        self.processed_isbns = set()
        self.isbn_lock = threading.Lock()
        
        # 请求配置
        self.min_delay = 0.5
        self.max_delay = 2.0
        self.max_retries = 5
        self.timeout = 30
        
        # 中断处理
        self.interrupted = False
        signal.signal(signal.SIGINT, self.signal_handler)

        # 加载已处理的状态
        self.load_processed_state()
        
        # 已知的有效图书链接模式
        self.known_book_patterns = [
            "/booksCenter/book_",
            "/book_",
        ]
        
        # 搜索策略
        self.search_strategies = [
            self.search_from_homepage,
            self.search_from_book_center,
            self.search_from_categories,
            self.search_from_sitemap,
        ]
        
    def setup_advanced_logging(self):
        """设置高级日志系统"""
        # 主日志
        main_log_file = os.path.join(self.logs_dir, f'crawler_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        
        # 创建多个日志记录器
        self.logger = logging.getLogger('main')
        self.performance_logger = logging.getLogger('performance')
        self.error_logger = logging.getLogger('error')
        
        # 设置日志级别
        self.logger.setLevel(logging.DEBUG)
        self.performance_logger.setLevel(logging.INFO)
        self.error_logger.setLevel(logging.WARNING)
        
        # 创建格式化器
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(threadName)s] - %(message)s'
        )
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        
        # 主日志处理器
        main_handler = logging.FileHandler(main_log_file, encoding='utf-8')
        main_handler.setFormatter(detailed_formatter)
        self.logger.addHandler(main_handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(simple_formatter)
        console_handler.setLevel(logging.INFO)
        self.logger.addHandler(console_handler)
        
        # 性能日志处理器
        perf_handler = logging.FileHandler(
            os.path.join(self.logs_dir, 'performance.log'), encoding='utf-8'
        )
        perf_handler.setFormatter(detailed_formatter)
        self.performance_logger.addHandler(perf_handler)
        
        # 错误日志处理器
        error_handler = logging.FileHandler(
            os.path.join(self.logs_dir, 'errors.log'), encoding='utf-8'
        )
        error_handler.setFormatter(detailed_formatter)
        self.error_logger.addHandler(error_handler)
        
    def signal_handler(self, signum, frame):
        """处理中断信号"""
        self.logger.warning("接收到中断信号，正在安全退出...")
        self.interrupted = True
        
    def get_session(self):
        """从池中获取会话"""
        return self.session_pool.get()
        
    def return_session(self, session):
        """归还会话到池"""
        self.session_pool.put(session)
        
    def smart_delay(self):
        """智能延迟策略"""
        base_delay = random.uniform(self.min_delay, self.max_delay)
        
        # 根据成功率调整延迟
        if self.stats['total_requests'] > 10:
            success_rate = self.stats['successful_requests'] / self.stats['total_requests']
            if success_rate < 0.7:
                base_delay *= 2  # 成功率低时增加延迟
            elif success_rate > 0.9:
                base_delay *= 0.8  # 成功率高时减少延迟
                
        time.sleep(base_delay)
        
    def safe_request(self, url: str, session=None, max_retries=None) -> Optional[requests.Response]:
        """高级安全请求方法"""
        if max_retries is None:
            max_retries = self.max_retries
            
        own_session = session is None
        if own_session:
            session = self.get_session()
            
        try:
            for attempt in range(max_retries):
                if self.interrupted:
                    return None
                    
                try:
                    self.smart_delay()
                    
                    # 记录请求
                    self.stats['total_requests'] += 1
                    start_time = time.time()
                    
                    response = session.get(url, timeout=self.timeout)
                    response.raise_for_status()
                    response.encoding = 'utf-8'
                    
                    # 记录成功
                    self.stats['successful_requests'] += 1
                    request_time = time.time() - start_time
                    
                    self.performance_logger.info(
                        f"请求成功: {url} - 耗时: {request_time:.2f}s - 状态码: {response.status_code}"
                    )
                    
                    return response
                    
                except requests.exceptions.RequestException as e:
                    self.stats['failed_requests'] += 1
                    wait_time = (2 ** attempt) + random.uniform(0, 1)
                    
                    self.error_logger.warning(
                        f"请求失败 (尝试 {attempt + 1}/{max_retries}): {url} - 错误: {str(e)} - 等待: {wait_time:.1f}s"
                    )
                    
                    if attempt < max_retries - 1:
                        time.sleep(wait_time)
                    else:
                        self.error_logger.error(f"请求最终失败: {url}")
                        self.stats['failed_urls'].add(url)
                        
        finally:
            if own_session:
                self.return_session(session)
                
        return None

    def is_duplicate_isbn(self, isbn: str) -> bool:
        """检查ISBN是否已存在"""
        if not isbn:
            return False
        with self.isbn_lock:
            return isbn in self.processed_isbns

    def add_processed_isbn(self, isbn: str) -> bool:
        """添加已处理的ISBN"""
        if not isbn:
            return False
        with self.isbn_lock:
            if isbn in self.processed_isbns:
                return False
            self.processed_isbns.add(isbn)
            return True

    def load_processed_state(self):
        """加载已处理的状态"""
        state_file = os.path.join(self.output_dir, 'processed_state.json')
        if os.path.exists(state_file):
            try:
                with open(state_file, 'r', encoding='utf-8') as f:
                    state = json.load(f)
                    self.stats['processed_urls'] = set(state.get('processed_urls', []))
                    self.processed_isbns = set(state.get('processed_isbns', []))
                    self.logger.info(f"📋 加载状态: {len(self.stats['processed_urls'])} URLs, {len(self.processed_isbns)} ISBNs")
            except Exception as e:
                self.logger.warning(f"加载状态文件失败: {str(e)}")

    def save_processed_state(self):
        """保存已处理的状态"""
        state_file = os.path.join(self.output_dir, 'processed_state.json')
        try:
            state = {
                'processed_urls': list(self.stats['processed_urls']),
                'processed_isbns': list(self.processed_isbns),
                'last_update': datetime.now().isoformat()
            }
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)
            self.logger.debug(f"💾 保存状态: {len(self.stats['processed_urls'])} URLs, {len(self.processed_isbns)} ISBNs")
        except Exception as e:
            self.logger.error(f"保存状态文件失败: {str(e)}")

    def extract_comprehensive_book_info(self, soup: BeautifulSoup, url: str) -> BookInfo:
        """全面提取图书信息"""
        book = BookInfo(url=url, crawl_time=datetime.now().isoformat())

        try:
            # 提取标题 - 多种策略
            title_selectors = [
                'h4', '.book-title', 'h1', 'h2', 'h3',
                '[class*="title"]', '[id*="title"]'
            ]

            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem and title_elem.get_text(strip=True):
                    book.title = title_elem.get_text(strip=True)
                    break

            # 提取封面图片 - 增强策略
            img_selectors = [
                'img[src*="bigbookimg"]', 'img[src*="smallbookimg"]',
                'img[src*="upload"]', 'img[src*="book"]',
                '.book-cover img', '.cover img', 'img[alt*="封面"]'
            ]

            for selector in img_selectors:
                img_elem = soup.select_one(selector)
                if img_elem and img_elem.get('src'):
                    src = img_elem.get('src')
                    if 'bigbookimg' in src:  # 优先大图
                        book.cover_image = src
                        break
                    elif not book.cover_image:  # 备选图片
                        book.cover_image = src

            # 提取详细信息 - 正则表达式增强
            text_content = soup.get_text()

            # 定价
            price_patterns = [
                r'定价[：:]\s*(\d+(?:\.\d+)?)\s*元',
                r'价格[：:]\s*(\d+(?:\.\d+)?)\s*元',
                r'￥\s*(\d+(?:\.\d+)?)',
                r'(\d+(?:\.\d+)?)\s*元'
            ]
            for pattern in price_patterns:
                match = re.search(pattern, text_content)
                if match:
                    book.price = match.group(1) + '元'
                    break

            # ISBN
            isbn_patterns = [
                r'ISBN[：:]\s*([0-9\-X]{10,17})',
                r'书号[：:]\s*([0-9\-X]{10,17})',
                r'([0-9\-X]{13})', r'([0-9\-X]{10})'
            ]
            for pattern in isbn_patterns:
                match = re.search(pattern, text_content)
                if match:
                    book.isbn = match.group(1)
                    break

            # 印次
            edition_patterns = [
                r'印次[：:]\s*([^\s\n]+)',
                r'版次[：:]\s*([^\s\n]+)',
                r'第(\d+)版', r'(\d+-\d+)'
            ]
            for pattern in edition_patterns:
                match = re.search(pattern, text_content)
                if match:
                    book.edition = match.group(1)
                    break

            # 出版日期
            date_patterns = [
                r'出版日期[：:]\s*(\d{4}\.\d{2}\.\d{2})',
                r'出版时间[：:]\s*(\d{4}\.\d{2}\.\d{2})',
                r'(\d{4}年\d{1,2}月)', r'(\d{4}-\d{2}-\d{2})'
            ]
            for pattern in date_patterns:
                match = re.search(pattern, text_content)
                if match:
                    book.publish_date = match.group(1)
                    break

            # 作者
            author_patterns = [
                r'作者[：:]\s*([^\n\r]+?)(?:\s|$)',
                r'著者[：:]\s*([^\n\r]+?)(?:\s|$)',
                r'编者[：:]\s*([^\n\r]+?)(?:\s|$)'
            ]
            for pattern in author_patterns:
                match = re.search(pattern, text_content)
                if match:
                    book.author = match.group(1).strip()
                    break

            # 页数
            pages_patterns = [
                r'页数[：:]\s*(\d+)',
                r'(\d+)\s*页', r'共\s*(\d+)\s*页'
            ]
            for pattern in pages_patterns:
                match = re.search(pattern, text_content)
                if match:
                    book.pages = match.group(1) + '页'
                    break

            # 下载封面图片
            if book.cover_image and book.title:
                cover_filename = self.download_cover_image_advanced(
                    book.cover_image, book.isbn, book.title
                )
                if cover_filename:
                    book.cover_image_file = cover_filename

            # 验证数据完整性
            book.success = bool(book.title and (book.isbn or book.price))

            # ISBN去重检查
            if book.success and book.isbn:
                if self.is_duplicate_isbn(book.isbn):
                    book.success = False
                    book.error_msg = f"重复ISBN: {book.isbn}"
                    self.logger.warning(f"⚠ 发现重复ISBN: {book.isbn} - {book.title}")
                else:
                    self.add_processed_isbn(book.isbn)

            if book.success:
                self.stats['books_found'] += 1
                self.logger.info(f"✓ 成功提取图书: {book.title}")
            else:
                if not book.error_msg:
                    book.error_msg = "关键信息缺失"
                self.logger.warning(f"⚠ 图书信息问题: {book.error_msg} - {url}")

        except Exception as e:
            book.error_msg = str(e)
            self.error_logger.error(f"✗ 提取图书信息出错: {url} - {str(e)}")

        return book

    def download_cover_image_advanced(self, img_url: str, isbn: str, title: str) -> Optional[str]:
        """高级图片下载方法"""
        if not img_url:
            return None

        try:
            full_img_url = urljoin(self.base_url, img_url)

            # 生成文件名
            img_ext = os.path.splitext(urlparse(img_url).path)[1] or '.jpg'
            safe_title = re.sub(r'[^\w\s-]', '', title)[:50]

            if isbn:
                filename = f"{isbn}_{safe_title}{img_ext}"
            else:
                # 使用URL哈希作为备用标识
                url_hash = hashlib.md5(img_url.encode()).hexdigest()[:8]
                filename = f"{url_hash}_{safe_title}{img_ext}"

            filepath = os.path.join(self.images_dir, filename)

            # 检查文件是否已存在
            if os.path.exists(filepath):
                self.logger.debug(f"图片已存在，跳过: {filename}")
                return filename

            # 下载图片
            session = self.get_session()
            try:
                response = self.safe_request(full_img_url, session=session, max_retries=3)
                if response and response.content:
                    # 验证图片内容
                    if len(response.content) > 1024:  # 至少1KB
                        with open(filepath, 'wb') as f:
                            f.write(response.content)

                        self.stats['images_downloaded'] += 1
                        self.logger.info(f"📷 下载图片: {filename} ({len(response.content)} bytes)")
                        return filename
                    else:
                        self.logger.warning(f"图片文件过小: {full_img_url}")
                else:
                    self.logger.warning(f"下载图片失败: {full_img_url}")

            finally:
                self.return_session(session)

        except Exception as e:
            self.error_logger.error(f"下载图片出错: {img_url} - {str(e)}")

        return None

    def search_from_homepage(self) -> List[str]:
        """从首页搜索图书链接"""
        self.logger.info("🔍 策略1: 从首页搜索图书链接...")
        book_links = []

        try:
            response = self.safe_request(f"{self.base_url}/index.html")
            if response:
                soup = BeautifulSoup(response.text, 'html.parser')
                links = soup.find_all('a', href=True)

                for link in links:
                    href = link.get('href')
                    if href and any(pattern in href for pattern in self.known_book_patterns):
                        if href not in book_links:
                            book_links.append(href)

                self.logger.info(f"从首页找到 {len(book_links)} 个图书链接")

        except Exception as e:
            self.error_logger.error(f"搜索首页时出错: {str(e)}")

        return book_links

    def search_from_book_center(self) -> List[str]:
        """从图书中心搜索链接"""
        self.logger.info("🔍 策略2: 从图书中心搜索链接...")
        book_links = []

        try:
            response = self.safe_request(f"{self.base_url}/booksCenter/books_index.html")
            if response:
                soup = BeautifulSoup(response.text, 'html.parser')
                links = soup.find_all('a', href=True)

                for link in links:
                    href = link.get('href')
                    if href and any(pattern in href for pattern in self.known_book_patterns):
                        if href not in book_links:
                            book_links.append(href)

                self.logger.info(f"从图书中心找到 {len(book_links)} 个图书链接")

        except Exception as e:
            self.error_logger.error(f"搜索图书中心时出错: {str(e)}")

        return book_links

    def search_from_categories(self) -> List[str]:
        """从分类页面搜索链接"""
        self.logger.info("🔍 策略3: 从分类页面搜索链接...")
        book_links = []

        # 主要分类ID
        category_ids = [2000, 2010, 2020, 2025, 2030, 2035, 2040, 2050, 2060, 2070, 2080]
        years = [2025, 2024, 2023]

        try:
            for category_id in category_ids[:3]:  # 限制分类数量以提高效率
                for year in years[:2]:  # 限制年份
                    if self.interrupted:
                        break

                    url = f"{self.base_url}/booksCenter/allbooks.html?id={category_id}&year={year}"
                    response = self.safe_request(url)

                    if response:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        links = soup.find_all('a', href=True)

                        category_books = 0
                        for link in links:
                            href = link.get('href')
                            if href and any(pattern in href for pattern in self.known_book_patterns):
                                if href not in book_links:
                                    book_links.append(href)
                                    category_books += 1

                        self.logger.debug(f"分类 {category_id} 年份 {year}: {category_books} 个链接")

            self.logger.info(f"从分类页面找到 {len(book_links)} 个图书链接")

        except Exception as e:
            self.error_logger.error(f"搜索分类页面时出错: {str(e)}")

        return book_links

    def search_from_sitemap(self) -> List[str]:
        """从站点地图搜索链接"""
        self.logger.info("🔍 策略4: 从站点地图搜索链接...")
        book_links = []

        try:
            # 尝试常见的站点地图URL
            sitemap_urls = [
                f"{self.base_url}/sitemap.xml",
                f"{self.base_url}/sitemap.html",
                f"{self.base_url}/robots.txt"
            ]

            for sitemap_url in sitemap_urls:
                response = self.safe_request(sitemap_url)
                if response:
                    content = response.text
                    # 使用正则表达式查找图书链接
                    book_pattern = r'((?:/booksCenter)?/book_\d+\.html)'
                    matches = re.findall(book_pattern, content)

                    for match in matches:
                        if match not in book_links:
                            book_links.append(match)

            self.logger.info(f"从站点地图找到 {len(book_links)} 个图书链接")

        except Exception as e:
            self.error_logger.error(f"搜索站点地图时出错: {str(e)}")

        return book_links

    def discover_book_links(self, max_links=100) -> List[str]:
        """智能发现图书链接"""
        self.logger.info(f"🚀 开始智能发现图书链接 (最多 {max_links} 个)...")
        all_links = []

        # 执行所有搜索策略
        for i, strategy in enumerate(self.search_strategies, 1):
            if self.interrupted:
                break

            try:
                self.logger.info(f"执行搜索策略 {i}/{len(self.search_strategies)}")
                links = strategy()

                # 去重并添加
                for link in links:
                    if link not in all_links:
                        all_links.append(link)

                self.logger.info(f"策略 {i} 完成，累计发现 {len(all_links)} 个链接")

                # 如果已经找到足够的链接，提前结束
                if len(all_links) >= max_links:
                    break

            except Exception as e:
                self.error_logger.error(f"搜索策略 {i} 执行失败: {str(e)}")

        # 限制链接数量
        final_links = all_links[:max_links]
        self.logger.info(f"🎯 链接发现完成，共找到 {len(final_links)} 个有效链接")

        return final_links

    def process_single_book(self, book_url: str) -> Optional[BookInfo]:
        """处理单本图书"""
        if book_url in self.stats['processed_urls']:
            return None

        self.stats['processed_urls'].add(book_url)

        try:
            full_url = urljoin(self.base_url, book_url)
            response = self.safe_request(full_url)

            if response:
                soup = BeautifulSoup(response.text, 'html.parser')
                book_info = self.extract_comprehensive_book_info(soup, full_url)

                if book_info.success:
                    with self.data_lock:
                        self.books_data.append(book_info)
                    return book_info

        except Exception as e:
            self.error_logger.error(f"处理图书时出错: {book_url} - {str(e)}")

        return None

    def save_comprehensive_data(self):
        """保存全面的数据"""
        if not self.books_data:
            self.logger.warning("没有数据需要保存")
            return

        try:
            # 保存JSON格式
            json_file = os.path.join(self.output_dir, 'books_comprehensive.json')
            with open(json_file, 'w', encoding='utf-8') as f:
                json_data = [asdict(book) for book in self.books_data]
                json.dump(json_data, f, ensure_ascii=False, indent=2)

            # 保存CSV格式
            csv_file = os.path.join(self.output_dir, 'books_comprehensive.csv')
            if self.books_data:
                fieldnames = [
                    'title', 'author', 'price', 'edition', 'isbn', 'publish_date',
                    'publisher', 'pages', 'category', 'cover_image', 'cover_image_file',
                    'description', 'url', 'crawl_time', 'success', 'error_msg'
                ]

                with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()

                    for book in self.books_data:
                        writer.writerow(asdict(book))

            # 保存统计信息
            stats_file = os.path.join(self.output_dir, 'crawl_statistics.json')
            with open(stats_file, 'w', encoding='utf-8') as f:
                stats_copy = self.stats.copy()
                # 转换集合为列表以便JSON序列化
                stats_copy['processed_urls'] = list(stats_copy['processed_urls'])
                stats_copy['failed_urls'] = list(stats_copy['failed_urls'])
                stats_copy['processed_isbns'] = list(self.processed_isbns)
                stats_copy['unique_isbns_count'] = len(self.processed_isbns)
                json.dump(stats_copy, f, ensure_ascii=False, indent=2)

            # 保存处理状态
            self.save_processed_state()

            self.logger.info(f"📊 数据保存完成:")
            self.logger.info(f"  - JSON: {json_file}")
            self.logger.info(f"  - CSV: {csv_file}")
            self.logger.info(f"  - 统计: {stats_file}")

        except Exception as e:
            self.error_logger.error(f"保存数据时出错: {str(e)}")

    def print_final_statistics(self):
        """打印最终统计信息"""
        self.stats['end_time'] = datetime.now()

        if self.stats['start_time']:
            duration = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
        else:
            duration = 0

        print("\n" + "="*60)
        print("🎉 爬取完成 - 最终统计报告")
        print("="*60)
        print(f"⏱️  总耗时: {duration:.1f} 秒")
        print(f"🌐 总请求数: {self.stats['total_requests']}")
        print(f"✅ 成功请求: {self.stats['successful_requests']}")
        print(f"❌ 失败请求: {self.stats['failed_requests']}")
        print(f"📚 发现图书: {self.stats['books_found']}")
        print(f"📖 唯一ISBN: {len(self.processed_isbns)}")
        print(f"🖼️  下载图片: {self.stats['images_downloaded']}")

        if self.stats['total_requests'] > 0:
            success_rate = self.stats['successful_requests'] / self.stats['total_requests'] * 100
            print(f"📈 成功率: {success_rate:.1f}%")

        if duration > 0:
            books_per_minute = self.stats['books_found'] / (duration / 60)
            print(f"⚡ 效率: {books_per_minute:.1f} 本/分钟")

        print("="*60)

    def crawl_books_advanced(self, max_books=100, use_threading=None):
        """高级图书爬取方法"""
        if use_threading is None:
            use_threading = self.enable_threading

        self.stats['start_time'] = datetime.now()
        self.logger.info(f"🚀 开始高级爬取模式 (最多 {max_books} 本图书)")

        try:
            # 发现图书链接
            book_links = self.discover_book_links(max_books * 2)  # 多发现一些链接作为备用

            if not book_links:
                self.logger.error("❌ 未发现任何图书链接")
                return

            # 限制处理数量
            book_links = book_links[:max_books]
            self.logger.info(f"📋 准备处理 {len(book_links)} 个图书链接")

            # 选择处理方式
            if use_threading and len(book_links) > 5:
                self.logger.info(f"🔄 使用多线程模式 ({self.max_workers} 线程)")
                self._crawl_with_threading(book_links)
            else:
                self.logger.info("🔄 使用单线程模式")
                self._crawl_sequential(book_links)

            # 保存数据
            self.save_comprehensive_data()

            # 显示统计
            self.print_final_statistics()

        except Exception as e:
            self.error_logger.error(f"爬取过程中出现严重错误: {str(e)}")
        finally:
            self.logger.info("🏁 爬取流程结束")

    def _crawl_sequential(self, book_links: List[str]):
        """顺序爬取"""
        for i, book_link in enumerate(tqdm(book_links, desc="📚 爬取图书"), 1):
            if self.interrupted:
                self.logger.warning("⚠️ 用户中断，停止爬取")
                break

            self.logger.debug(f"处理 {i}/{len(book_links)}: {book_link}")
            book_info = self.process_single_book(book_link)

            # 每处理20本书保存一次
            if i % 20 == 0:
                self.save_comprehensive_data()
                self.logger.info(f"📊 中间保存完成 ({i}/{len(book_links)})")

    def _crawl_with_threading(self, book_links: List[str]):
        """多线程爬取"""
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_url = {
                executor.submit(self.process_single_book, link): link
                for link in book_links
            }

            # 处理结果
            completed = 0
            with tqdm(total=len(book_links), desc="📚 多线程爬取") as pbar:
                for future in as_completed(future_to_url):
                    if self.interrupted:
                        self.logger.warning("⚠️ 用户中断，停止爬取")
                        break

                    url = future_to_url[future]
                    try:
                        result = future.result()
                        completed += 1
                        pbar.update(1)

                        # 每完成50个任务保存一次
                        if completed % 50 == 0:
                            self.save_comprehensive_data()
                            self.logger.info(f"📊 中间保存完成 ({completed}/{len(book_links)})")

                    except Exception as e:
                        self.error_logger.error(f"处理 {url} 时出错: {str(e)}")
                        pbar.update(1)

def main():
    """主函数"""
    print("🌟 清华大学出版社高级图书爬虫 v2.0")
    print("=" * 50)

    # 配置选项
    print("请选择爬取配置:")
    print("1. 快速测试 (10本图书, 单线程)")
    print("2. 标准模式 (50本图书, 多线程)")
    print("3. 深度模式 (100本图书, 多线程)")
    print("4. 自定义配置")

    choice = input("\n请输入选择 (1-4): ").strip()

    if choice == "1":
        max_books, use_threading, max_workers = 10, False, 1
    elif choice == "2":
        max_books, use_threading, max_workers = 50, True, 3
    elif choice == "3":
        max_books, use_threading, max_workers = 100, True, 5
    elif choice == "4":
        try:
            max_books = int(input("图书数量 (1-200): "))
            max_books = max(1, min(200, max_books))

            threading_choice = input("使用多线程? (y/n): ").lower()
            use_threading = threading_choice in ['y', 'yes', '是']

            if use_threading:
                max_workers = int(input("线程数量 (1-10): "))
                max_workers = max(1, min(10, max_workers))
            else:
                max_workers = 1
        except:
            print("输入无效，使用默认配置")
            max_books, use_threading, max_workers = 50, True, 3
    else:
        print("无效选择，使用默认配置")
        max_books, use_threading, max_workers = 50, True, 3

    print(f"\n📋 配置确认:")
    print(f"  - 图书数量: {max_books}")
    print(f"  - 多线程: {'是' if use_threading else '否'}")
    print(f"  - 线程数: {max_workers}")
    print(f"  - 输出目录: books_data_advanced/")

    print("\n🚀 开始爬取...")

    # 创建爬虫实例
    crawler = AdvancedTupCrawler(max_workers=max_workers, enable_threading=use_threading)

    try:
        crawler.crawl_books_advanced(max_books=max_books, use_threading=use_threading)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
        crawler.save_comprehensive_data()
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        crawler.save_comprehensive_data()

if __name__ == "__main__":
    main()
