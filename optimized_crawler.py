#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版清华大学出版社图书信息爬虫
专门处理大规模爬取的情况
"""

import requests
from bs4 import BeautifulSoup
import json
import csv
import os
import time
import random
import re
from urllib.parse import urljoin
from tqdm import tqdm
import logging
from datetime import datetime

class OptimizedTupCrawler:
    def __init__(self):
        """初始化爬虫"""
        self.base_url = "https://www.tup.com.cn"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
        
        # 创建输出目录
        self.output_dir = "books_data"
        self.images_dir = os.path.join(self.output_dir, "images")
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.images_dir, exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
        # 存储所有图书数据
        self.books_data = []
        
        # 请求间隔设置（秒）
        self.min_delay = 2
        self.max_delay = 5
        
        # 已知的图书链接（手动收集或从其他渠道获得）
        self.known_book_links = [
            "/booksCenter/book_10520401.html",  # 高等数学（上册）
            # 可以添加更多已知链接
        ]
        
    def setup_logging(self):
        """设置日志记录"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(self.output_dir, 'optimized_crawler.log'), encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def random_delay(self):
        """随机延迟"""
        delay = random.uniform(self.min_delay, self.max_delay)
        time.sleep(delay)
        
    def safe_request(self, url, max_retries=3):
        """安全的HTTP请求"""
        for attempt in range(max_retries):
            try:
                self.random_delay()
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                response.encoding = 'utf-8'
                return response
            except requests.exceptions.RequestException as e:
                self.logger.warning(f"请求失败 (尝试 {attempt + 1}/{max_retries}): {url} - {str(e)}")
                if attempt == max_retries - 1:
                    self.logger.error(f"请求最终失败: {url}")
                    return None
                time.sleep(2 ** attempt)
        return None
        
    def download_cover_image(self, img_url, book_isbn, book_title):
        """下载图书封面图片"""
        if not img_url:
            return None
            
        try:
            full_img_url = urljoin(self.base_url, img_url)
            img_ext = os.path.splitext(img_url)[1] or '.jpg'
            
            safe_title = re.sub(r'[^\w\s-]', '', book_title)[:50]
            if book_isbn:
                filename = f"{book_isbn}_{safe_title}{img_ext}"
            else:
                filename = f"{safe_title}{img_ext}"
                
            filepath = os.path.join(self.images_dir, filename)
            
            if os.path.exists(filepath):
                self.logger.info(f"封面图片已存在，跳过下载: {filename}")
                return filename
                
            response = self.safe_request(full_img_url)
            if response and response.content:
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                self.logger.info(f"成功下载封面图片: {filename}")
                return filename
            else:
                self.logger.warning(f"下载封面图片失败: {full_img_url}")
                return None
                
        except Exception as e:
            self.logger.error(f"下载封面图片时出错: {img_url} - {str(e)}")
            return None

    def extract_book_info(self, soup, url):
        """从图书详情页面提取信息"""
        book_info = {
            'url': url,
            'title': '',
            'price': '',
            'edition': '',
            'isbn': '',
            'publish_date': '',
            'publisher': '清华大学出版社',
            'cover_image': '',
            'cover_image_file': '',
            'crawl_time': datetime.now().isoformat()
        }
        
        try:
            # 提取图书名称
            title_elem = soup.find('h4')
            if title_elem:
                book_info['title'] = title_elem.get_text(strip=True)
                
            # 提取封面图片
            img_tags = soup.find_all('img', src=True)
            for img in img_tags:
                src = img.get('src')
                if src and ('bigbookimg' in src or 'smallbookimg' in src or 'upload' in src):
                    if 'bigbookimg' in src:
                        book_info['cover_image'] = src
                        break
                    elif not book_info['cover_image']:
                        book_info['cover_image'] = src
                        
            # 提取其他信息
            text_content = soup.get_text()
            
            price_match = re.search(r'定价[：:]\s*(\d+(?:\.\d+)?)\s*元', text_content)
            if price_match:
                book_info['price'] = price_match.group(1) + '元'
                
            edition_match = re.search(r'印次[：:]\s*([^\s\n]+)', text_content)
            if edition_match:
                book_info['edition'] = edition_match.group(1)
                
            isbn_match = re.search(r'ISBN[：:]\s*([0-9\-X]+)', text_content)
            if isbn_match:
                book_info['isbn'] = isbn_match.group(1)
                
            date_match = re.search(r'出版日期[：:]\s*(\d{4}\.\d{2}\.\d{2})', text_content)
            if date_match:
                book_info['publish_date'] = date_match.group(1)
                
            # 下载封面图片
            if book_info['cover_image']:
                cover_filename = self.download_cover_image(
                    book_info['cover_image'], 
                    book_info['isbn'], 
                    book_info['title']
                )
                if cover_filename:
                    book_info['cover_image_file'] = cover_filename
                
        except Exception as e:
            self.logger.error(f"解析图书信息时出错: {url} - {str(e)}")
            
        return book_info
        
    def get_book_details(self, book_url):
        """获取单本图书的详细信息"""
        full_url = urljoin(self.base_url, book_url)
        response = self.safe_request(full_url)
        
        if not response:
            return None
            
        soup = BeautifulSoup(response.text, 'html.parser')
        book_info = self.extract_book_info(soup, full_url)
        
        if book_info['title']:
            self.logger.info(f"成功获取图书信息: {book_info['title']}")
            return book_info
        else:
            self.logger.warning(f"未能提取到图书标题: {full_url}")
            return None
            
    def search_books_from_homepage(self):
        """从首页搜索图书链接"""
        self.logger.info("正在从首页搜索图书链接...")
        
        search_urls = [
            f"{self.base_url}/index.html",
            f"{self.base_url}/booksCenter/books_index.html",
        ]
        
        all_book_links = []
        
        for url in search_urls:
            self.logger.info(f"搜索页面: {url}")
            response = self.safe_request(url)
            
            if response:
                soup = BeautifulSoup(response.text, 'html.parser')
                links = soup.find_all('a', href=True)
                
                for link in links:
                    href = link.get('href')
                    if href and 'book_' in href and '.html' in href:
                        all_book_links.append(href)
        
        unique_links = list(set(all_book_links))
        self.logger.info(f"从首页搜索到 {len(unique_links)} 个图书链接")
        return unique_links
        
    def save_data(self):
        """保存数据到文件"""
        if not self.books_data:
            self.logger.warning("没有数据需要保存")
            return
            
        # 保存为JSON格式
        json_file = os.path.join(self.output_dir, 'books_data.json')
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.books_data, f, ensure_ascii=False, indent=2)
            
        # 保存为CSV格式
        csv_file = os.path.join(self.output_dir, 'books_data.csv')
        if self.books_data:
            fieldnames = ['title', 'price', 'edition', 'isbn', 'publish_date', 'publisher', 'cover_image', 'cover_image_file', 'url', 'crawl_time']
            with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(self.books_data)
                
        self.logger.info(f"数据已保存到 {json_file} 和 {csv_file}")
        
    def crawl_limited_books(self, max_books=50):
        """爬取有限数量的图书（用于测试和演示）"""
        self.logger.info(f"开始爬取清华大学出版社图书信息（限制 {max_books} 本）...")
        
        # 获取图书链接
        book_links = self.known_book_links.copy()
        
        # 尝试从首页搜索更多链接
        homepage_links = self.search_books_from_homepage()
        book_links.extend(homepage_links)
        
        # 去重并限制数量
        unique_links = list(set(book_links))[:max_books]
        
        if not unique_links:
            self.logger.error("未获取到任何图书链接")
            return
            
        self.logger.info(f"将爬取 {len(unique_links)} 本图书")
        
        # 爬取每本图书的详细信息
        for i, book_link in enumerate(tqdm(unique_links, desc="爬取图书信息")):
            self.logger.info(f"正在处理第 {i+1}/{len(unique_links)} 本图书")
            
            book_info = self.get_book_details(book_link)
            if book_info:
                self.books_data.append(book_info)
                
                # 每处理10本书保存一次数据
                if len(self.books_data) % 10 == 0:
                    self.save_data()
                    
        # 最终保存所有数据
        self.save_data()
        
        self.logger.info(f"爬取完成！总共获取到 {len(self.books_data)} 本图书的信息")

def main():
    """主函数"""
    crawler = OptimizedTupCrawler()
    
    print("=== 优化版清华大学出版社图书爬虫 ===")
    print("请选择爬取模式:")
    print("1. 爬取少量图书（10本，快速测试）")
    print("2. 爬取中等数量图书（50本，推荐）")
    print("3. 爬取大量图书（100本，较慢）")
    
    choice = input("\n请输入选择 (1-3): ").strip()
    
    if choice == "1":
        max_books = 10
    elif choice == "2":
        max_books = 50
    elif choice == "3":
        max_books = 100
    else:
        print("无效选择，使用默认值（50本）")
        max_books = 50
    
    try:
        crawler.crawl_limited_books(max_books)
    except KeyboardInterrupt:
        print("\n用户中断了程序")
        crawler.save_data()
    except Exception as e:
        crawler.logger.error(f"程序执行出错: {str(e)}")
        crawler.save_data()

if __name__ == "__main__":
    main()
