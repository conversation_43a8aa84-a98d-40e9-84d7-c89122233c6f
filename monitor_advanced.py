#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级爬虫监控工具
实时监控爬虫性能和进度
"""

import os
import json
import time
import glob
from datetime import datetime
from pathlib import Path

class AdvancedCrawlerMonitor:
    def __init__(self):
        self.output_dir = "books_data_advanced"
        self.logs_dir = os.path.join(self.output_dir, "logs")
        self.images_dir = os.path.join(self.output_dir, "images")
        
    def get_latest_log_file(self):
        """获取最新的日志文件"""
        if not os.path.exists(self.logs_dir):
            return None
            
        log_files = glob.glob(os.path.join(self.logs_dir, "crawler_*.log"))
        if not log_files:
            return None
            
        return max(log_files, key=os.path.getctime)
        
    def read_statistics(self):
        """读取统计信息"""
        stats_file = os.path.join(self.output_dir, "crawl_statistics.json")
        if os.path.exists(stats_file):
            try:
                with open(stats_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return None
        
    def count_books_data(self):
        """统计图书数据"""
        json_file = os.path.join(self.output_dir, "books_comprehensive.json")
        if os.path.exists(json_file):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return len(data)
            except:
                pass
        return 0
        
    def count_images(self):
        """统计图片数量"""
        if not os.path.exists(self.images_dir):
            return 0
            
        image_files = [f for f in os.listdir(self.images_dir) 
                      if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp'))]
        return len(image_files)
        
    def get_log_tail(self, lines=10):
        """获取日志尾部"""
        log_file = self.get_latest_log_file()
        if not log_file:
            return []
            
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                return all_lines[-lines:] if all_lines else []
        except:
            return []
            
    def analyze_performance(self):
        """分析性能日志"""
        perf_log = os.path.join(self.logs_dir, "performance.log")
        if not os.path.exists(perf_log):
            return {}
            
        try:
            with open(perf_log, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            total_requests = len(lines)
            if total_requests == 0:
                return {}
                
            # 分析请求时间
            times = []
            for line in lines:
                if "耗时:" in line:
                    try:
                        time_part = line.split("耗时:")[1].split("s")[0].strip()
                        times.append(float(time_part))
                    except:
                        continue
                        
            if times:
                avg_time = sum(times) / len(times)
                max_time = max(times)
                min_time = min(times)
                
                return {
                    'total_requests': total_requests,
                    'avg_response_time': avg_time,
                    'max_response_time': max_time,
                    'min_response_time': min_time
                }
                
        except:
            pass
            
        return {}
        
    def display_status(self):
        """显示当前状态"""
        print("\n" + "="*70)
        print("🔍 清华大学出版社高级爬虫 - 实时监控")
        print("="*70)
        
        # 基本统计
        books_count = self.count_books_data()
        images_count = self.count_images()
        
        print(f"📚 已爬取图书: {books_count} 本")
        print(f"🖼️  已下载图片: {images_count} 张")
        
        # 读取详细统计
        stats = self.read_statistics()
        if stats:
            print(f"🌐 总请求数: {stats.get('total_requests', 0)}")
            print(f"✅ 成功请求: {stats.get('successful_requests', 0)}")
            print(f"❌ 失败请求: {stats.get('failed_requests', 0)}")
            
            if stats.get('total_requests', 0) > 0:
                success_rate = stats.get('successful_requests', 0) / stats.get('total_requests', 1) * 100
                print(f"📈 成功率: {success_rate:.1f}%")
                
        # 性能分析
        perf = self.analyze_performance()
        if perf:
            print(f"⚡ 平均响应时间: {perf.get('avg_response_time', 0):.2f}s")
            print(f"🚀 最快响应: {perf.get('min_response_time', 0):.2f}s")
            print(f"🐌 最慢响应: {perf.get('max_response_time', 0):.2f}s")
            
        # 最新日志
        print("\n📋 最新日志 (最后5条):")
        print("-" * 70)
        log_lines = self.get_log_tail(5)
        for line in log_lines:
            print(f"  {line.strip()}")
            
        print("="*70)
        print(f"⏰ 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    def monitor_realtime(self, interval=10):
        """实时监控"""
        print("🚀 启动实时监控模式 (按 Ctrl+C 退出)")
        print(f"📊 刷新间隔: {interval} 秒")
        
        try:
            while True:
                # 清屏 (在支持的终端中)
                os.system('clear' if os.name == 'posix' else 'cls')
                
                self.display_status()
                
                print(f"\n⏳ {interval} 秒后自动刷新...")
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n\n👋 监控已停止")
            
    def show_detailed_report(self):
        """显示详细报告"""
        print("\n" + "="*80)
        print("📊 详细爬取报告")
        print("="*80)
        
        # 文件信息
        files_info = []
        
        # JSON文件
        json_file = os.path.join(self.output_dir, "books_comprehensive.json")
        if os.path.exists(json_file):
            size = os.path.getsize(json_file)
            files_info.append(f"📄 JSON数据: {size:,} bytes")
            
        # CSV文件
        csv_file = os.path.join(self.output_dir, "books_comprehensive.csv")
        if os.path.exists(csv_file):
            size = os.path.getsize(csv_file)
            files_info.append(f"📊 CSV数据: {size:,} bytes")
            
        # 图片目录
        if os.path.exists(self.images_dir):
            image_files = [f for f in os.listdir(self.images_dir) 
                          if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp'))]
            total_size = sum(os.path.getsize(os.path.join(self.images_dir, f)) for f in image_files)
            files_info.append(f"🖼️  图片文件: {len(image_files)} 个, {total_size:,} bytes")
            
        # 日志文件
        if os.path.exists(self.logs_dir):
            log_files = os.listdir(self.logs_dir)
            total_log_size = sum(os.path.getsize(os.path.join(self.logs_dir, f)) for f in log_files)
            files_info.append(f"📝 日志文件: {len(log_files)} 个, {total_log_size:,} bytes")
            
        for info in files_info:
            print(info)
            
        # 错误分析
        error_log = os.path.join(self.logs_dir, "errors.log")
        if os.path.exists(error_log):
            try:
                with open(error_log, 'r', encoding='utf-8') as f:
                    error_lines = f.readlines()
                    
                print(f"\n⚠️  错误统计: {len(error_lines)} 条错误记录")
                
                if error_lines:
                    print("最近的错误:")
                    for line in error_lines[-3:]:
                        print(f"  {line.strip()}")
                        
            except:
                pass
                
        print("="*80)

def main():
    """主函数"""
    monitor = AdvancedCrawlerMonitor()
    
    print("🔍 高级爬虫监控工具")
    print("请选择监控模式:")
    print("1. 显示当前状态")
    print("2. 实时监控 (10秒刷新)")
    print("3. 实时监控 (30秒刷新)")
    print("4. 详细报告")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == "1":
        monitor.display_status()
    elif choice == "2":
        monitor.monitor_realtime(10)
    elif choice == "3":
        monitor.monitor_realtime(30)
    elif choice == "4":
        monitor.show_detailed_report()
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
