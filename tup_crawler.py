#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清华大学出版社图书信息爬虫
作者: AI Assistant
日期: 2024
"""

import requests
from bs4 import BeautifulSoup
import json
import csv
import os
import time
import random
import re
from urllib.parse import urljoin, urlparse
from tqdm import tqdm
import logging
from datetime import datetime

class TupCrawler:
    def __init__(self):
        """初始化爬虫"""
        self.base_url = "https://www.tup.com.cn"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # 创建输出目录
        self.output_dir = "books_data"
        self.images_dir = os.path.join(self.output_dir, "images")
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.images_dir, exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
        # 存储所有图书数据
        self.books_data = []
        
        # 请求间隔设置（秒）
        self.min_delay = 1
        self.max_delay = 3
        
    def setup_logging(self):
        """设置日志记录"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(self.output_dir, 'crawler.log'), encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def random_delay(self):
        """随机延迟，避免对服务器造成过大负担"""
        delay = random.uniform(self.min_delay, self.max_delay)
        time.sleep(delay)
        
    def safe_request(self, url, max_retries=3):
        """安全的HTTP请求，包含重试机制"""
        for attempt in range(max_retries):
            try:
                self.random_delay()
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                response.encoding = 'utf-8'
                return response
            except requests.exceptions.RequestException as e:
                self.logger.warning(f"请求失败 (尝试 {attempt + 1}/{max_retries}): {url} - {str(e)}")
                if attempt == max_retries - 1:
                    self.logger.error(f"请求最终失败: {url}")
                    return None
                time.sleep(2 ** attempt)  # 指数退避
        return None
        
    def extract_book_info(self, soup, url):
        """从图书详情页面提取信息"""
        book_info = {
            'url': url,
            'title': '',
            'price': '',
            'edition': '',
            'isbn': '',
            'publish_date': '',
            'publisher': '清华大学出版社',
            'crawl_time': datetime.now().isoformat()
        }
        
        try:
            # 提取图书名称
            title_elem = soup.find('h4')
            if title_elem:
                book_info['title'] = title_elem.get_text(strip=True)
                
            # 提取定价、印次、ISBN、出版日期
            # 这些信息通常在特定的文本节点中
            text_content = soup.get_text()
            
            # 提取定价
            price_match = re.search(r'定价[：:]\s*(\d+(?:\.\d+)?)\s*元', text_content)
            if price_match:
                book_info['price'] = price_match.group(1) + '元'
                
            # 提取印次
            edition_match = re.search(r'印次[：:]\s*([^\s\n]+)', text_content)
            if edition_match:
                book_info['edition'] = edition_match.group(1)
                
            # 提取ISBN
            isbn_match = re.search(r'ISBN[：:]\s*([0-9\-X]+)', text_content)
            if isbn_match:
                book_info['isbn'] = isbn_match.group(1)
                
            # 提取出版日期
            date_match = re.search(r'出版日期[：:]\s*(\d{4}\.\d{2}\.\d{2})', text_content)
            if date_match:
                book_info['publish_date'] = date_match.group(1)
                
        except Exception as e:
            self.logger.error(f"解析图书信息时出错: {url} - {str(e)}")
            
        return book_info
        
    def get_book_details(self, book_url):
        """获取单本图书的详细信息"""
        full_url = urljoin(self.base_url, book_url)
        response = self.safe_request(full_url)
        
        if not response:
            return None
            
        soup = BeautifulSoup(response.text, 'html.parser')
        book_info = self.extract_book_info(soup, full_url)
        
        if book_info['title']:
            self.logger.info(f"成功获取图书信息: {book_info['title']}")
            return book_info
        else:
            self.logger.warning(f"未能提取到图书标题: {full_url}")
            return None
            
    def get_book_links_from_category(self, category_id, year=None):
        """从特定分类获取图书链接"""
        book_links = []
        
        # 构建分类URL
        if year:
            category_url = f"{self.base_url}/booksCenter/allbooks.html?id={category_id}&year={year}"
        else:
            category_url = f"{self.base_url}/booksCenter/allbooks.html?id={category_id}"
            
        self.logger.info(f"正在获取分类 {category_id} 的图书链接...")
        
        response = self.safe_request(category_url)
        if not response:
            return book_links
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找图书链接 - 需要根据实际页面结构调整选择器
        # 这里使用通用的方法查找包含book_的链接
        links = soup.find_all('a', href=True)
        for link in links:
            href = link.get('href')
            if href and 'book_' in href and '.html' in href:
                full_link = urljoin(self.base_url, href)
                if full_link not in book_links:
                    book_links.append(href)
                    
        self.logger.info(f"从分类 {category_id} 获取到 {len(book_links)} 个图书链接")
        return book_links
        
    def get_all_book_links(self):
        """获取所有图书链接"""
        all_links = []
        
        # 主要分类ID列表
        categories = [
            2000,  # 计算机与电子信息
            2010,  # 理工
            2020,  # 经管与人文
            2025,  # 文化与传播
            2030,  # 法律与艺术
            2035,  # 生物与医学
            2040,  # 语言
            2050,  # 科普与少儿
            2060,  # 辞书工具书学术书
            2070,  # 考试书
            2080,  # 基础与职业教育
        ]
        
        # 年份列表
        years = [2025, 2024, 2023, 2022, 2021, 2020]
        
        for category in categories:
            for year in years:
                links = self.get_book_links_from_category(category, year)
                all_links.extend(links)
                
        # 去重
        unique_links = list(set(all_links))
        self.logger.info(f"总共获取到 {len(unique_links)} 个唯一图书链接")
        
        return unique_links
        
    def save_data(self):
        """保存数据到文件"""
        if not self.books_data:
            self.logger.warning("没有数据需要保存")
            return
            
        # 保存为JSON格式
        json_file = os.path.join(self.output_dir, 'books_data.json')
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.books_data, f, ensure_ascii=False, indent=2)
            
        # 保存为CSV格式
        csv_file = os.path.join(self.output_dir, 'books_data.csv')
        if self.books_data:
            fieldnames = ['title', 'price', 'edition', 'isbn', 'publish_date', 'publisher', 'url', 'crawl_time']
            with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(self.books_data)
                
        self.logger.info(f"数据已保存到 {json_file} 和 {csv_file}")
        
    def crawl_all_books(self):
        """爬取所有图书信息"""
        self.logger.info("开始爬取清华大学出版社图书信息...")
        
        # 获取所有图书链接
        book_links = self.get_all_book_links()
        
        if not book_links:
            self.logger.error("未获取到任何图书链接")
            return
            
        # 爬取每本图书的详细信息
        for i, book_link in enumerate(tqdm(book_links, desc="爬取图书信息")):
            self.logger.info(f"正在处理第 {i+1}/{len(book_links)} 本图书")
            
            book_info = self.get_book_details(book_link)
            if book_info:
                self.books_data.append(book_info)
                
                # 每处理100本书保存一次数据
                if len(self.books_data) % 100 == 0:
                    self.save_data()
                    
        # 最终保存所有数据
        self.save_data()
        
        self.logger.info(f"爬取完成！总共获取到 {len(self.books_data)} 本图书的信息")

def main():
    """主函数"""
    crawler = TupCrawler()
    try:
        crawler.crawl_all_books()
    except KeyboardInterrupt:
        print("\n用户中断了程序")
        crawler.save_data()
    except Exception as e:
        crawler.logger.error(f"程序执行出错: {str(e)}")
        crawler.save_data()

if __name__ == "__main__":
    main()
