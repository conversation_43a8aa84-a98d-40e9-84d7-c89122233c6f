#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫进度监控脚本
"""

import os
import json
import time
from datetime import datetime

def monitor_progress():
    """监控爬虫进度"""
    books_data_dir = "books_data"
    log_file = os.path.join(books_data_dir, "crawler.log")
    json_file = os.path.join(books_data_dir, "books_data.json")
    images_dir = os.path.join(books_data_dir, "images")
    
    print("=== 清华大学出版社爬虫进度监控 ===")
    print("按 Ctrl+C 退出监控\n")
    
    last_log_size = 0
    last_book_count = 0
    last_image_count = 0
    
    try:
        while True:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"[{current_time}] 检查进度...")
            
            # 检查日志文件
            if os.path.exists(log_file):
                log_size = os.path.getsize(log_file)
                if log_size > last_log_size:
                    print(f"  📝 日志文件更新: {log_size:,} bytes (+{log_size - last_log_size:,})")
                    last_log_size = log_size
                    
                    # 读取最新的日志行
                    with open(log_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        if lines:
                            latest_line = lines[-1].strip()
                            print(f"  📄 最新日志: {latest_line}")
            
            # 检查JSON数据文件
            book_count = 0
            if os.path.exists(json_file):
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        books_data = json.load(f)
                        book_count = len(books_data)
                        
                    if book_count > last_book_count:
                        print(f"  📚 图书数据更新: {book_count} 本 (+{book_count - last_book_count})")
                        last_book_count = book_count
                        
                        # 显示最新的图书信息
                        if books_data:
                            latest_book = books_data[-1]
                            print(f"  📖 最新图书: {latest_book.get('title', '未知')}")
                except:
                    pass
            
            # 检查图片目录
            image_count = 0
            if os.path.exists(images_dir):
                image_files = [f for f in os.listdir(images_dir) 
                              if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp'))]
                image_count = len(image_files)
                
                if image_count > last_image_count:
                    print(f"  🖼️  图片文件更新: {image_count} 张 (+{image_count - last_image_count})")
                    last_image_count = image_count
            
            # 显示总体统计
            print(f"  📊 当前统计: {book_count} 本图书, {image_count} 张图片")
            print("-" * 60)
            
            # 等待30秒后再次检查
            time.sleep(30)
            
    except KeyboardInterrupt:
        print("\n监控已停止")
        
        # 显示最终统计
        print(f"\n=== 最终统计 ===")
        print(f"图书总数: {book_count}")
        print(f"图片总数: {image_count}")
        
        if os.path.exists(log_file):
            log_size = os.path.getsize(log_file)
            print(f"日志大小: {log_size:,} bytes")

def show_current_status():
    """显示当前状态"""
    books_data_dir = "books_data"
    log_file = os.path.join(books_data_dir, "crawler.log")
    json_file = os.path.join(books_data_dir, "books_data.json")
    images_dir = os.path.join(books_data_dir, "images")
    
    print("=== 当前爬虫状态 ===")
    
    # 检查日志文件
    if os.path.exists(log_file):
        log_size = os.path.getsize(log_file)
        print(f"📝 日志文件: {log_size:,} bytes")
        
        # 显示最后几行日志
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            print("📄 最近日志:")
            for line in lines[-5:]:
                print(f"   {line.strip()}")
    else:
        print("📝 日志文件: 不存在")
    
    # 检查数据文件
    if os.path.exists(json_file):
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                books_data = json.load(f)
                print(f"📚 图书数据: {len(books_data)} 本")
                
                if books_data:
                    latest_book = books_data[-1]
                    print(f"📖 最新图书: {latest_book.get('title', '未知')}")
                    print(f"🕒 最新时间: {latest_book.get('crawl_time', '未知')}")
        except:
            print("📚 图书数据: 文件损坏")
    else:
        print("📚 图书数据: 不存在")
    
    # 检查图片目录
    if os.path.exists(images_dir):
        image_files = [f for f in os.listdir(images_dir) 
                      if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp'))]
        print(f"🖼️  图片文件: {len(image_files)} 张")
        
        if image_files:
            total_size = sum(os.path.getsize(os.path.join(images_dir, f)) for f in image_files)
            print(f"💾 图片总大小: {total_size:,} bytes ({total_size/1024/1024:.1f} MB)")
    else:
        print("🖼️  图片目录: 不存在")

if __name__ == "__main__":
    print("请选择操作:")
    print("1. 实时监控爬虫进度")
    print("2. 显示当前状态")
    
    choice = input("\n请输入选择 (1-2): ").strip()
    
    if choice == "1":
        monitor_progress()
    elif choice == "2":
        show_current_status()
    else:
        print("无效选择")
