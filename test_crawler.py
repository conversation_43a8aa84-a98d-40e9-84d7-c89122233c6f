#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试爬虫功能的脚本
"""

from tup_crawler import TupCrawler
import json

def test_single_book():
    """测试单本图书的信息提取和图片下载"""
    crawler = TupCrawler()
    
    # 测试示例图书页面
    test_url = "/booksCenter/book_10520401.html"
    
    print("正在测试单本图书信息提取...")
    book_info = crawler.get_book_details(test_url)
    
    if book_info:
        print("成功提取图书信息:")
        print(json.dumps(book_info, ensure_ascii=False, indent=2))
        
        # 检查是否成功下载了封面图片
        if book_info.get('cover_image_file'):
            print(f"\n封面图片已下载: {book_info['cover_image_file']}")
        else:
            print("\n未能下载封面图片")
    else:
        print("提取图书信息失败")

def test_category_links():
    """测试分类页面链接提取"""
    crawler = TupCrawler()
    
    print("正在测试分类页面链接提取...")
    # 测试一个小分类
    links = crawler.get_book_links_from_category(2000, 2025)
    
    print(f"从分类2000(计算机与电子信息)获取到 {len(links)} 个链接")
    if links:
        print("前5个链接:")
        for i, link in enumerate(links[:5]):
            print(f"  {i+1}. {link}")

def test_limited_crawl():
    """测试有限数量的图书爬取"""
    crawler = TupCrawler()
    
    print("正在测试有限数量图书爬取...")
    
    # 获取少量图书链接进行测试
    links = crawler.get_book_links_from_category(2000, 2025)
    test_links = links[:3]  # 只测试前3本书
    
    print(f"将测试 {len(test_links)} 本图书")
    
    for i, link in enumerate(test_links):
        print(f"\n正在处理第 {i+1} 本图书: {link}")
        book_info = crawler.get_book_details(link)
        
        if book_info:
            crawler.books_data.append(book_info)
            print(f"  标题: {book_info.get('title', '未知')}")
            print(f"  ISBN: {book_info.get('isbn', '未知')}")
            print(f"  定价: {book_info.get('price', '未知')}")
            print(f"  封面图片: {book_info.get('cover_image_file', '未下载')}")
        else:
            print("  提取信息失败")
    
    # 保存测试数据
    crawler.save_data()
    print(f"\n测试完成，共处理 {len(crawler.books_data)} 本图书")

if __name__ == "__main__":
    print("=== 清华大学出版社爬虫测试 ===\n")
    
    # 选择测试模式
    print("请选择测试模式:")
    print("1. 测试单本图书信息提取")
    print("2. 测试分类页面链接提取")
    print("3. 测试有限数量图书爬取")
    
    choice = input("\n请输入选择 (1-3): ").strip()
    
    if choice == "1":
        test_single_book()
    elif choice == "2":
        test_category_links()
    elif choice == "3":
        test_limited_crawl()
    else:
        print("无效选择，退出测试")
