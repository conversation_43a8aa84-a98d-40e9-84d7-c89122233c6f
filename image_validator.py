#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片验证工具
用于检查下载的图书封面图片是否有效
"""

import os
import json
from pathlib import Path

def validate_images():
    """验证下载的图片文件"""
    books_data_dir = "books_data"
    images_dir = os.path.join(books_data_dir, "images")
    json_file = os.path.join(books_data_dir, "books_data.json")
    
    if not os.path.exists(images_dir):
        print("图片目录不存在")
        return
        
    if not os.path.exists(json_file):
        print("数据文件不存在")
        return
    
    # 读取图书数据
    with open(json_file, 'r', encoding='utf-8') as f:
        books_data = json.load(f)
    
    print(f"数据文件中共有 {len(books_data)} 本图书")
    
    # 统计图片下载情况
    total_books = len(books_data)
    books_with_cover_url = 0
    books_with_downloaded_image = 0
    valid_image_files = 0
    
    # 检查实际存在的图片文件
    if os.path.exists(images_dir):
        image_files = [f for f in os.listdir(images_dir) 
                      if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp'))]
        valid_image_files = len(image_files)
        print(f"图片目录中共有 {valid_image_files} 个图片文件")
    
    # 分析每本书的图片情况
    for book in books_data:
        if book.get('cover_image'):
            books_with_cover_url += 1
            
        if book.get('cover_image_file'):
            books_with_downloaded_image += 1
            
            # 检查文件是否真实存在
            image_path = os.path.join(images_dir, book['cover_image_file'])
            if not os.path.exists(image_path):
                print(f"警告: 图片文件不存在 - {book['cover_image_file']} (书名: {book.get('title', '未知')})")
    
    # 输出统计结果
    print("\n=== 图片下载统计 ===")
    print(f"总图书数量: {total_books}")
    print(f"有封面URL的图书: {books_with_cover_url} ({books_with_cover_url/total_books*100:.1f}%)")
    print(f"已下载图片的图书: {books_with_downloaded_image} ({books_with_downloaded_image/total_books*100:.1f}%)")
    print(f"实际图片文件数量: {valid_image_files}")
    
    if books_with_cover_url > 0:
        download_success_rate = books_with_downloaded_image / books_with_cover_url * 100
        print(f"图片下载成功率: {download_success_rate:.1f}%")

def list_sample_images(count=10):
    """列出示例图片文件"""
    images_dir = os.path.join("books_data", "images")
    
    if not os.path.exists(images_dir):
        print("图片目录不存在")
        return
    
    image_files = [f for f in os.listdir(images_dir) 
                  if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp'))]
    
    print(f"\n=== 示例图片文件 (前{min(count, len(image_files))}个) ===")
    for i, filename in enumerate(image_files[:count]):
        file_path = os.path.join(images_dir, filename)
        file_size = os.path.getsize(file_path)
        print(f"{i+1:2d}. {filename} ({file_size:,} bytes)")

def check_image_sizes():
    """检查图片文件大小分布"""
    images_dir = os.path.join("books_data", "images")
    
    if not os.path.exists(images_dir):
        print("图片目录不存在")
        return
    
    image_files = [f for f in os.listdir(images_dir) 
                  if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp'))]
    
    if not image_files:
        print("没有找到图片文件")
        return
    
    sizes = []
    for filename in image_files:
        file_path = os.path.join(images_dir, filename)
        size = os.path.getsize(file_path)
        sizes.append(size)
    
    sizes.sort()
    total_size = sum(sizes)
    avg_size = total_size / len(sizes)
    
    print(f"\n=== 图片大小统计 ===")
    print(f"图片文件数量: {len(sizes)}")
    print(f"总大小: {total_size:,} bytes ({total_size/1024/1024:.1f} MB)")
    print(f"平均大小: {avg_size:,.0f} bytes ({avg_size/1024:.1f} KB)")
    print(f"最小文件: {sizes[0]:,} bytes")
    print(f"最大文件: {sizes[-1]:,} bytes")
    print(f"中位数: {sizes[len(sizes)//2]:,} bytes")

if __name__ == "__main__":
    print("=== 图书封面图片验证工具 ===\n")
    
    print("请选择操作:")
    print("1. 验证图片下载情况")
    print("2. 列出示例图片文件")
    print("3. 检查图片大小统计")
    print("4. 执行所有检查")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == "1":
        validate_images()
    elif choice == "2":
        list_sample_images()
    elif choice == "3":
        check_image_sizes()
    elif choice == "4":
        validate_images()
        list_sample_images()
        check_image_sizes()
    else:
        print("无效选择，退出程序")
